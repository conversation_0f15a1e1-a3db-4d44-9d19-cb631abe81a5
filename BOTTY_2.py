#!/usr/bin/env python3
"""
Amico Bot – 100 % locale
- Database JSON su ./data/
- Auto-promemoria tramite LLM
- Check-in adattivi
- Gestisce “Reply”
"""
import os, json, time, datetime, logging, uuid, pathlib, asyncio
from typing import List, Dict, Any
from telegram import Update, Message
from telegram.ext import (
    Application, CommandHandler, MessageHandler,
    filters, ContextTypes
)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from openai import OpenAI
import requests

# ---------- CONFIG ----------
BOT_TOKEN   = os.getenv("TG_TOKEN", "5000871892:AAEWdQFtPN8mvf2C-Yqwh8Ii6SLfsSi8dT0")
LM_URL      = os.getenv("LM_URL", "http://localhost:1234/v1")
MODEL       = os.getenv("LM_MODEL", "qwen/qwen3-8b")

# Inizializzazione client OpenAI
client = OpenAI(
    base_url=LM_URL,
    api_key="lm-studio"
)

DATA_DIR = pathlib.Path("./data")
DATA_DIR.mkdir(exist_ok=True)
DB_FILE = DATA_DIR / "memories.json"
APPTS_FILE = DATA_DIR / "appointments.json"
USER_META_FILE = DATA_DIR / "users.json"

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
log = logging.getLogger(__name__)

# ---------- PICCOLA UTIL JSON ----------
class TinyDB:
    """Minimale key-value JSON on-disk"""
    def __init__(self, path: pathlib.Path):
        self.path = path
        if not path.exists():
            path.write_text("[]")

    def _load(self):
        with open(self.path, "r", encoding="utf-8") as f:
            return json.load(f)

    def _save(self, data):
        with open(self.path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)

    def insert(self, record: Dict):
        data = self._load()
        data.append(record)
        self._save(data)

    def find(self, predicate) -> List[Dict]:
        return [r for r in self._load() if predicate(r)]

    def remove(self, predicate):
        data = self._load()
        data = [r for r in data if not predicate(r)]
        self._save(data)

mem_db   = TinyDB(DB_FILE)
appt_db  = TinyDB(APPTS_FILE)
user_db  = TinyDB(USER_META_FILE)

# ---------- MEMORIA ----------
def save_msg(uid: int, role: str, text: str, tags=None):
    mem_db.insert({
        "user_id": uid,
        "role": role,
        "text": text,
        "tags": tags or [],
        "ts": datetime.datetime.utcnow().isoformat()
    })

def get_thread(uid: int, limit=30) -> List[Dict]:
    msgs = [m for m in mem_db._load() if m["user_id"] == uid]
    msgs.sort(key=lambda x: x["ts"])
    return msgs[-limit:]

# ---------- PROMEMORIA ----------
def save_appointment(uid: int, when: datetime.datetime, text: str):
    appt_db.insert({
        "id": str(uuid.uuid4()),
        "user_id": uid,
        "text": text,
        "when": when.isoformat()
    })

async def fire_reminders(app: Application):
    now = datetime.datetime.utcnow()
    for ap in appt_db.find(lambda a: datetime.datetime.fromisoformat(a["when"]) <= now):
        try:
            await app.bot.send_message(chat_id=ap["user_id"], text=f"⏰ {ap['text']}")
            appt_db.remove(lambda x: x["id"] == ap["id"])
        except Exception as e:
            log.error(e)

# ---------- CHECK-IN ADATTIVO ----------
def user_meta(uid: int) -> Dict:
    u = user_db.find(lambda x: x["user_id"] == uid)
    return u[0] if u else {"checkin_period_hours": 8, "last_checkin": None}

def set_user_meta(uid: int, **kw):
    user_db.remove(lambda x: x["user_id"] == uid)
    user_db.insert({**{"user_id": uid}, **kw})

async def adaptive_checkin(app: Application):
    now = datetime.datetime.utcnow()
    for u in user_db._load():
        uid = u["user_id"]
        period = datetime.timedelta(hours=u.get("checkin_period_hours", 8))
        last = datetime.datetime.fromisoformat(u["last_checkin"]) if u.get("last_checkin") else now - period*2
        if now - last >= period:
            try:
                await app.bot.send_message(chat_id=uid, text="👋 Ciao, come stai?")
                set_user_meta(uid, last_checkin=now.isoformat())
            except Exception:
                pass

# ---------- CHAT CON LLM ----------
async def llm_chat(uid: int, user_text: str) -> str:
    system = (
        "Sei un amico locale, empatico e proattivo. "
        "Se necessario, puoi programmare promemoria per l'utente. "
        "Per farlo rispondi con JSON inline: {\"reminder\": {\"minutes\": 15, \"text\": \"andare dal dentista\"}}\n"
        "Non usare markdown nel JSON. Rispondi sempre in italiano."
    )
    thread = get_thread(uid)
    msgs = [{"role": "system", "content": system}]
    msgs += [{"role": m["role"], "content": m["text"]} for m in thread]
    msgs.append({"role": "user", "content": user_text})

    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=msgs,
            temperature=0.7,
            max_tokens=512
        )
        reply = response.choices[0].message.content.strip()

        # Rimuovi i tag <think> e </think> dal modello Qwen
        import re
        reply = re.sub(r'<think>.*?</think>', '', reply, flags=re.DOTALL).strip()

        # Se la risposta è vuota dopo aver rimosso i tag <think>, usa un messaggio di default
        if not reply:
            reply = "🤔 Sto pensando... fammi riflettere un momento."

        return reply
    except Exception as e:
        log.error(e)
        return "😢 Non riesco a rispondere adesso."

# ---------- HANDLERS ----------
async def start(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    uid = update.effective_user.id
    if not user_meta(uid):
        set_user_meta(uid, checkin_period_hours=8, last_checkin=None)
    await update.message.reply_text(
        "👋 Ciao, sono il tuo amico locale!\n"
        "Parla pure, ricorderò tutto e mi occuperò io dei promemoria quando serve."
    )

async def handle(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    uid = update.effective_user.id
    text = update.message.text
    save_msg(uid, "user", text)

    reply = await llm_chat(uid, text)

    # Eventuale JSON reminder dal LLM
    try:
        lines = reply.splitlines()
        for line in lines:
            if line.strip().startswith("{"):
                data = json.loads(line.strip())
                if "reminder" in data:
                    mins = data["reminder"]["minutes"]
                    rem_text = data["reminder"]["text"]
                    when = datetime.datetime.utcnow() + datetime.timedelta(minutes=mins)
                    save_appointment(uid, when, rem_text)
                    reply = reply.replace(line, "").strip()
    except Exception:
        pass

    # Se dopo aver rimosso il JSON la risposta è vuota, usa un messaggio di default
    if not reply.strip():
        reply = "👍 Ho capito e ho preso nota!"

    save_msg(uid, "assistant", reply)
    await update.message.reply_text(reply, reply_to_message_id=update.message.message_id)

# ---------- MAIN ----------
def main():
    if BOT_TOKEN == "INSERISCI_TOKEN":
        print("Set TG_TOKEN")
        return

    app = Application.builder().token(BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle))

    # Scheduler - inizializzato dopo l'avvio dell'app
    async def post_init(application):
        sched = AsyncIOScheduler(timezone="UTC")
        sched.add_job(fire_reminders, "interval", minutes=1, args=[application])
        sched.add_job(adaptive_checkin, "interval", minutes=30, args=[application])
        sched.start()
        log.info("Scheduler avviato")

    app.post_init = post_init

    log.info("Bot avviato")
    app.run_polling()

if __name__ == "__main__":
    main()