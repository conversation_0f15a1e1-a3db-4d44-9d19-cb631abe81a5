#!/usr/bin/env python3
"""
Amico <PERSON>t – 100 % locale
- reply a QUALSIASI messaggio (passato/presente)
- decide da solo se rispondere (skip su messaggio incompleto)
- promemoria automatici
- DB locale JSON
"""
import os, json, uuid, pathlib, datetime, logging, asyncio, re
from typing import List, Dict, Any
from telegram import Update
from telegram.ext import (
    Application, CommandHandler, MessageHandler,
    filters, ContextTypes
)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from openai import OpenAI

# ---------- CONFIG ----------
BOT_TOKEN = os.getenv("TG_TOKEN", "5000871892:AAEWdQFtPN8mvf2C-Yqwh8Ii6SLfsSi8dT0")
LM_URL    = os.getenv("LM_URL",   "http://localhost:1234/v1")
MODEL     = os.getenv("LM_MODEL", "qwen/qwen3-8b")

client = OpenAI(base_url=LM_URL, api_key="lm-studio")

DATA_DIR = pathlib.Path("./data")
DATA_DIR.mkdir(exist_ok=True)
MEM_FILE   = DATA_DIR / "memories.json"
APPT_FILE  = DATA_DIR / "appointments.json"
USER_FILE  = DATA_DIR / "users.json"

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
log = logging.getLogger(__name__)

# ---------- UTIL JSON ----------
class TinyDB:
    def __init__(self, path: pathlib.Path):
        self.path = path
        if not path.exists():
            path.write_text("[]")
    def _load(self):
        with open(self.path, encoding="utf-8") as f:
            return json.load(f)
    def _save(self, data):
        with open(self.path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    def insert(self, record):
        data = self._load()
        data.append(record)
        self._save(data)
    def find(self, pred):
        return [r for r in self._load() if pred(r)]
    def remove(self, pred):
        data = self._load()
        data = [r for r in data if not pred(r)]
        self._save(data)

mem_db  = TinyDB(MEM_FILE)
appt_db = TinyDB(APPT_FILE)
user_db = TinyDB(USER_FILE)

# ---------- MEMORIA ----------
def save_msg(uid: int, role: str, text: str):
    mem_db.insert({
        "user_id": uid,
        "role": role,
        "text": text,
        "ts": datetime.datetime.utcnow().isoformat()
    })

def get_thread(uid: int, limit: int = 50) -> List[Dict[str, str]]:
    msgs = [m for m in mem_db._load() if m["user_id"] == uid]
    msgs.sort(key=lambda x: x["ts"])
    return msgs[-limit:]

# ---------- PROMEMORIA ----------
def save_appt(uid: int, mins: int, text: str):
    when = datetime.datetime.utcnow() + datetime.timedelta(minutes=mins)
    appt_db.insert({
        "id": str(uuid.uuid4()),
        "user_id": uid,
        "text": text,
        "when": when.isoformat()
    })

async def fire_reminders(app: Application):
    now = datetime.datetime.utcnow()
    for ap in appt_db.find(lambda a: datetime.datetime.fromisoformat(a["when"]) <= now):
        try:
            await app.bot.send_message(chat_id=ap["user_id"], text=f"⏰ {ap['text']}")
            appt_db.remove(lambda x: x["id"] == ap["id"])
        except Exception as e:
            log.error(e)

# ---------- CHAT ----------
async def llm_chat(uid: int, text: str, replied_text: str) -> tuple[str, bool]:
    system = (
        "Sei un amico empatico locale.\n"
        "1. Se l'utente ha usato la funzione 'reply' (risposta a un suo messaggio precedente), "
        "rispondi DIRETTAMENTE a quel messaggio.\n"
        "2. Se il messaggio è troppo vago, incompleto o non richiede risposta (es. 'ok', 'mah'), "
        "rispondi SOLO con la parola: __SKIP__\n"
        "3. Puoi programmare promemoria con JSON: {\"reminder\":{\"minutes\":15,\"text\":\"esame\"}}\n"
        "Rispondi sempre in italiano."
    )
    thread = get_thread(uid)
    msgs = [{"role": "system", "content": system}]
    msgs += [{"role": m["role"], "content": m["text"]} for m in thread]
    # aggiungo il messaggio di riferimento se c'è
    if replied_text:
        msgs.append({"role": "user", "content": f"[reply-to-previous]: {replied_text}"})
    msgs.append({"role": "user", "content": text})

    try:
        r = client.chat.completions.create(model=MODEL, messages=msgs, max_tokens=512, temperature=0.7)
        reply = r.choices[0].message.content.strip()
        reply = re.sub(r"<think>.*?</think>", "", reply, flags=re.S).strip()
        if reply == "__SKIP__":
            return "", False
        return reply, True
    except Exception as e:
        log.error(e)
        return "😢 Non riesco a rispondere.", True

# ---------- HANDLERS ----------
async def start(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    uid = update.effective_user.id
    if not user_db.find(lambda x: x["user_id"] == uid):
        user_db.insert({"user_id": uid, "checkin_last": None})
    await update.message.reply_text(
        "👋 Ciao! Sono il tuo amico locale.\n"
        "Puoi rispondere a qualunque tuo vecchio messaggio e io replicherò sopra."
    )

async def handle(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    uid = update.effective_user.id
    text = update.message.text
    replied_text = (update.message.reply_to_message.text or "") if update.message.reply_to_message else ""

    save_msg(uid, "user", text)
    reply, do_send = await llm_chat(uid, text, replied_text)

    # estrazione reminder
    try:
        data = json.loads(re.findall(r"\{.*?\}", reply, re.S)[0])
        if "reminder" in data:
            mins = data["reminder"]["minutes"]
            rem_text = data["reminder"]["text"]
            save_appt(uid, mins, rem_text)
            reply = reply.replace(json.dumps(data, ensure_ascii=False), "").strip()
    except:
        pass

    if not reply.strip():
        reply = "👍 Capito!"
    save_msg(uid, "assistant", reply)

    # sceglie dove mettere la risposta
    target_msg = update.message.reply_to_message or update.message
    if do_send:
        await target_msg.reply_text(reply)

# ---------- MAIN ----------
def main():
    if BOT_TOKEN == "INSERISCI_QUI_IL_TUO_TOKEN":
        print("Set TG_TOKEN")
        return
    app = Application.builder().token(BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle))

    async def post_init(app):
        sched = AsyncIOScheduler(timezone="UTC")
        sched.add_job(fire_reminders, "interval", minutes=1, args=[app])
        sched.start()
        log.info("Scheduler avviato")
    app.post_init = post_init

    log.info("Bot avviato")
    app.run_polling()

if __name__ == "__main__":
    main()