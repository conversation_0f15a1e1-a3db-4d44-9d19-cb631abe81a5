#!/usr/bin/env python3
"""
Amico <PERSON> – Telegram + LM Studio
Un solo file, zero dipendenze esterne oltre:
pip install python-telegram-bot pymongo apscheduler requests openai
"""

import os, datetime, json, logging, asyncio
from typing import Dict, List, Any
from pymongo import MongoClient
from telegram import Update
from telegram.ext import (
    Application, CommandHandler, MessageHandler,
    filters, ContextTypes, Defaults
)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
import openai, requests

# ------------------- CONFIGURAZIONE -------------------
BOT_TOKEN   = os.getenv("TG_TOKEN",   "5000871892:AAEWdQFtPN8mvf2C-Yqwh8Ii6SLfsSi8dT0")
MONGO_URL   = os.getenv("MONGO_URL",  "mongodb://localhost:27017")
LM_ENDPOINT = os.getenv("LM_URL",     "http://localhost:1234/v1")
MODEL_NAME  = os.getenv("LM_MODEL",   "qwen/qwen3-8b")

# Inizializzazione client
openai.api_base = LM_ENDPOINT
openai.api_key  = "lm-studio"

client = MongoClient(MONGO_URL)
db = client["amico_bot"]

# Collezioni
memories      = db["memories"]
appointments  = db["appointments"]
users         = db["users"]

# Logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
log = logging.getLogger(__name__)

# ------------------- FUNZIONI DI MEMORIA -------------------
def save_memory(user_id: int, role: str, content: str, tags: List[str]=None):
    memories.insert_one({
        "user_id": user_id,
        "role": role,
        "content": content,
        "timestamp": datetime.datetime.utcnow(),
        "tags": tags or []
    })

def get_context(user_id: int, limit: int = 10) -> List[Dict[str, str]]:
    msgs = list(memories.find({"user_id": user_id}).sort("timestamp", -1).limit(limit))
    return [{"role": m["role"], "content": m["content"]} for m in msgs[::-1]]

# ------------------- PROMEMORIA -------------------
async def send_reminders(app: Application):
    now = datetime.datetime.utcnow()
    due = list(appointments.find({"when": {"$lte": now}}))
    for ap in due:
        try:
            await app.bot.send_message(
                chat_id=ap["user_id"],
                text=f"⏰ Promemoria: {ap['text']}"
            )
            appointments.delete_one({"_id": ap["_id"]})
        except Exception as e:
            log.error(f"Errore invio promemoria: {e}")

async def checkin_job(app: Application):
    """Ogni 4 ore manda un check-in casuale agli utenti attivi nelle ultime 48h"""
    cutoff = datetime.datetime.utcnow() - datetime.timedelta(hours=48)
    recent_users = memories.distinct("user_id", {"timestamp": {"$gte": cutoff}})
    for uid in recent_users:
        if users.find_one({"user_id": uid, "checkin_enabled": True}):
            try:
                await app.bot.send_message(
                    chat_id=uid,
                    text="👋 Ciao! Come stai oggi?"
                )
            except Exception:
                pass  # utente bloccato

# ------------------- COMANDI TELEGRAM -------------------
async def start(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text(
        "👋 Ciao! Sono il tuo amico AI locale.\n"
        "Parla con me e ricorderò tutto.\n\n"
        "/ricorda <testo> per aggiungere un promemoria.\n"
        "/checkin on|off per abilitare/disabilitare i check-in."
    )
    users.update_one(
        {"user_id": update.effective_user.id},
        {"$setOnInsert": {"checkin_enabled": True}},
        upsert=True
    )

async def toggle_checkin(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    arg = ctx.args[0] if ctx.args else ""
    if arg.lower() in {"on", "1", "true"}:
        flag = True
    elif arg.lower() in {"off", "0", "false"}:
        flag = False
    else:
        await update.message.reply_text("Uso: /checkin on|off")
        return
    users.update_one(
        {"user_id": update.effective_user.id},
        {"$set": {"checkin_enabled": flag}},
        upsert=True
    )
    await update.message.reply_text(f"Check-in automatici: {'attivati' if flag else 'disattivati'}")

async def add_appointment(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    if not ctx.args:
        await update.message.reply_text("Uso: /ricorda fra <minuti> <messaggio>")
        return
    try:
        idx = ctx.args.index("fra")
        minutes = int(ctx.args[idx+1])
        text = " ".join(ctx.args[idx+2:])
        when = datetime.datetime.utcnow() + datetime.timedelta(minutes=minutes)
        appointments.insert_one({
            "user_id": update.effective_user.id,
            "text": text,
            "when": when
        })
        await update.message.reply_text(
            f"Promemoria impostato tra {minutes} minuti."
        )
    except (ValueError, IndexError):
        await update.message.reply_text("Errore nel formato. /ricorda fra 30 prendere la torta")

async def handle_text(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    text = update.message.text
    save_memory(user_id, "user", text)

    # Prepara prompt
    system = (
        "Sei un amico empatico. Usa la memoria fornita per dare risposte personalizzate, "
        "usa tono caloroso e familiare. Rispondi in italiano."
    )
    messages = [{"role": "system", "content": system}]
    messages += get_context(user_id, limit=10)

    # Chiamata LM Studio
    try:
        r = openai.ChatCompletion.create(
            model=MODEL_NAME,
            messages=messages,
            temperature=0.7,
            max_tokens=256
        )
        reply = r.choices[0].message.content.strip()
    except Exception as e:
        log.exception(e)
        reply = "😞 Oops, non riesco a parlarti al momento."

    save_memory(user_id, "assistant", reply)
    await update.message.reply_text(reply)

# ------------------- MAIN -------------------
def main():
    if BOT_TOKEN == "INSERISCI_QUI_IL_TUO_TOKEN":
        log.error("Imposta la variabile TG_TOKEN")
        return

    app = Application.builder().token(BOT_TOKEN).build()

    # Registra handlers
    app.add_handler(CommandHandler("start",   start))
    app.add_handler(CommandHandler("checkin", toggle_checkin))
    app.add_handler(CommandHandler("ricorda", add_appointment))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))

    # Scheduler
    sched = AsyncIOScheduler(timezone="UTC")
    sched.add_job(
        send_reminders,
        "interval",
        minutes=1,
        args=[app]
    )
    sched.add_job(
        checkin_job,
        "interval",
        hours=4,
        args=[app]
    )
    sched.start()

    log.info("Bot avviato – polling...")
    app.run_polling()

if __name__ == "__main__":
    main()