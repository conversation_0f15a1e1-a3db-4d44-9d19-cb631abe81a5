#!/usr/bin/env python3
"""
Amico Bot – Telegram + LM Studio (Versione locale senza MongoDB)
Un solo file, zero dipendenze esterne oltre:
pip install python-telegram-bot apscheduler requests openai
"""

import os, datetime, json, logging, asyncio
from typing import Dict, List, Any
from telegram import Update
from telegram.ext import (
    Application, CommandHandler, MessageHandler,
    filters, ContextTypes, Defaults
)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from openai import OpenAI
import requests

# ------------------- CONFIGURAZIONE -------------------
BOT_TOKEN   = os.getenv("TG_TOKEN",   "5000871892:AAEWdQFtPN8mvf2C-Yqwh8Ii6SLfsSi8dT0")
LM_ENDPOINT = os.getenv("LM_URL",     "http://localhost:1234/v1")
MODEL_NAME  = os.getenv("LM_MODEL",   "qwen/qwen3-8b")

# Inizializzazione client OpenAI
client = OpenAI(
    base_url=LM_ENDPOINT,
    api_key="lm-studio"
)

# Storage locale
STORAGE_FILE = "bot_storage.json"
if os.path.exists(STORAGE_FILE):
    with open(STORAGE_FILE, 'r', encoding='utf-8') as f:
        storage = json.load(f)
else:
    storage = {
        "memories": {},
        "appointments": [],
        "users": {}
    }

def save_storage():
    with open(STORAGE_FILE, 'w', encoding='utf-8') as f:
        json.dump(storage, f, indent=2, default=str)

# Logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
log = logging.getLogger(__name__)

# ------------------- FUNZIONI DI MEMORIA -------------------
def save_memory(user_id: int, role: str, content: str, tags: List[str]=None):
    if str(user_id) not in storage["memories"]:
        storage["memories"][str(user_id)] = []
    
    storage["memories"][str(user_id)].append({
        "role": role,
        "content": content,
        "timestamp": str(datetime.datetime.utcnow()),
        "tags": tags or []
    })
    
    # Mantieni solo gli ultimi 50 messaggi per utente
    if len(storage["memories"][str(user_id)]) > 50:
        storage["memories"][str(user_id)] = storage["memories"][str(user_id)][-50:]
    
    save_storage()

def get_context(user_id: int, limit: int = 10) -> List[Dict[str, str]]:
    if str(user_id) not in storage["memories"]:
        return []
    
    msgs = storage["memories"][str(user_id)][-limit:]
    return [{"role": m["role"], "content": m["content"]} for m in msgs]

# ------------------- PROMEMORIA -------------------
async def send_reminders(app: Application):
    now = datetime.datetime.utcnow()
    due_appointments = []
    
    for ap in storage["appointments"]:
        ap_time = datetime.datetime.fromisoformat(ap["when"])
        if ap_time <= now:
            due_appointments.append(ap)
    
    for ap in due_appointments:
        try:
            await app.bot.send_message(
                chat_id=ap["user_id"],
                text=f"⏰ Promemoria: {ap['text']}"
            )
            storage["appointments"].remove(ap)
        except Exception as e:
            log.error(f"Errore invio promemoria: {e}")
    
    if due_appointments:
        save_storage()

async def checkin_job(app: Application):
    """Ogni 4 ore manda un check-in casuale agli utenti attivi nelle ultime 48h"""
    cutoff = datetime.datetime.utcnow() - datetime.timedelta(hours=48)
    active_users = set()
    
    for user_id, memories in storage["memories"].items():
        for memory in memories:
            mem_time = datetime.datetime.fromisoformat(memory["timestamp"])
            if mem_time >= cutoff:
                active_users.add(int(user_id))
                break
    
    for uid in active_users:
        if storage["users"].get(str(uid), {}).get("checkin_enabled", True):
            try:
                await app.bot.send_message(
                    chat_id=uid,
                    text="👋 Ciao! Come stai oggi?"
                )
            except Exception:
                pass  # utente bloccato

# ------------------- COMANDI TELEGRAM -------------------
async def start(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text(
        "👋 Ciao! Sono il tuo amico AI locale.\n"
        "Parla con me e ricorderò tutto.\n\n"
        "/ricorda <testo> per aggiungere un promemoria.\n"
        "/checkin on|off per abilitare/disabilitare i check-in."
    )
    
    user_id = str(update.effective_user.id)
    if user_id not in storage["users"]:
        storage["users"][user_id] = {"checkin_enabled": True}
        save_storage()

async def toggle_checkin(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    arg = ctx.args[0] if ctx.args else ""
    if arg.lower() in {"on", "1", "true"}:
        flag = True
    elif arg.lower() in {"off", "0", "false"}:
        flag = False
    else:
        await update.message.reply_text("Uso: /checkin on|off")
        return
    
    user_id = str(update.effective_user.id)
    storage["users"][user_id] = {"checkin_enabled": flag}
    save_storage()
    
    await update.message.reply_text(f"Check-in automatici: {'attivati' if flag else 'disattivati'}")

async def add_appointment(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    if not ctx.args:
        await update.message.reply_text("Uso: /ricorda fra <minuti> <messaggio>")
        return
    
    try:
        idx = ctx.args.index("fra")
        minutes = int(ctx.args[idx+1])
        text = " ".join(ctx.args[idx+2:])
        when = datetime.datetime.utcnow() + datetime.timedelta(minutes=minutes)
        
        storage["appointments"].append({
            "user_id": update.effective_user.id,
            "text": text,
            "when": str(when)
        })
        save_storage()
        
        await update.message.reply_text(
            f"Promemoria impostato tra {minutes} minuti."
        )
    except (ValueError, IndexError):
        await update.message.reply_text("Errore nel formato. /ricorda fra 30 prendere la torta")

async def handle_text(update: Update, ctx: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    text = update.message.text
    save_memory(user_id, "user", text)

    # Prepara prompt
    system = (
        "Sei un amico empatico. Usa la memoria fornita per dare risposte personalizzate, "
        "usa tono caloroso e familiare. Rispondi in italiano."
    )
    messages = [{"role": "system", "content": system}]
    messages += get_context(user_id, limit=10)

    # Chiamata LM Studio
    try:
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=messages,
            temperature=0.7,
            max_tokens=256
        )
        reply = response.choices[0].message.content.strip()
    except Exception as e:
        log.exception(e)
        reply = "😞 Oops, non riesco a parlarti al momento."

    save_memory(user_id, "assistant", reply)
    await update.message.reply_text(reply)

# ------------------- MAIN -------------------
def main():
    if BOT_TOKEN == "INSERISCI_QUI_IL_TUO_TOKEN":
        log.error("Imposta la variabile TG_TOKEN")
        return

    app = Application.builder().token(BOT_TOKEN).build()

    # Registra handlers
    app.add_handler(CommandHandler("start",   start))
    app.add_handler(CommandHandler("checkin", toggle_checkin))
    app.add_handler(CommandHandler("ricorda", add_appointment))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))

    # Scheduler - inizializzato dopo l'avvio dell'app
    async def post_init(application):
        sched = AsyncIOScheduler(timezone="UTC")
        sched.add_job(
            send_reminders,
            "interval",
            minutes=1,
            args=[application]
        )
        sched.add_job(
            checkin_job,
            "interval",
            hours=4,
            args=[application]
        )
        sched.start()
        log.info("Scheduler avviato")

    app.post_init = post_init

    log.info("Bot avviato – polling...")
    app.run_polling()

if __name__ == "__main__":
    main()
